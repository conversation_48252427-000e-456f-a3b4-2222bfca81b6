-- Hospital Management System - Complete Database Setup
-- <PERSON><PERSON>t tổng hợp để thiết lập toàn bộ database với relationships, policies và indexes

-- ============================================================================
-- STEP 1: CREATE HELPER FUNCTIONS
-- ============================================================================

\echo 'Step 1: Creating helper functions...'

-- Function để lấy thông tin foreign keys
CREATE OR REPLACE FUNCTION get_foreign_keys()
RETURNS TABLE (
    table_name text,
    column_name text,
    foreign_table_name text,
    foreign_column_name text,
    constraint_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tc.table_name::text,
        kcu.column_name::text,
        ccu.table_name::text AS foreign_table_name,
        ccu.column_name::text AS foreign_column_name,
        tc.constraint_name::text
    FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
    ORDER BY tc.table_name, kcu.column_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin RLS policies
CREATE OR REPLACE FUNCTION get_rls_policies()
RETURNS TABLE (
    schemaname text,
    tablename text,
    policyname text,
    permissive text,
    roles text[],
    cmd text,
    qual text,
    with_check text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.schemaname::text,
        p.tablename::text,
        p.policyname::text,
        p.permissive::text,
        p.roles,
        p.cmd::text,
        p.qual::text,
        p.with_check::text
    FROM pg_policies p
    WHERE p.schemaname = 'public'
    ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin indexes
CREATE OR REPLACE FUNCTION get_table_indexes()
RETURNS TABLE (
    schemaname text,
    tablename text,
    indexname text,
    indexdef text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.nspname::text AS schemaname,
        t.relname::text AS tablename,
        i.relname::text AS indexname,
        pg_get_indexdef(i.oid)::text AS indexdef
    FROM pg_index x
    JOIN pg_class i ON i.oid = x.indexrelid
    JOIN pg_class t ON t.oid = x.indrelid
    JOIN pg_namespace n ON n.oid = t.relnamespace
    WHERE n.nspname = 'public'
      AND NOT x.indisprimary  -- Exclude primary key indexes
      AND i.relname NOT LIKE '%_pkey'  -- Exclude primary key indexes
    ORDER BY t.relname, i.relname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_foreign_keys() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_rls_policies() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_table_indexes() TO authenticated, anon;

\echo 'Helper functions created successfully!'

-- ============================================================================
-- STEP 2: ADD FOREIGN KEY RELATIONSHIPS
-- ============================================================================

\echo 'Step 2: Adding foreign key relationships...'

-- DOCTORS table relationships
DO $$
BEGIN
    -- doctors.profile_id -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_profile_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: doctors.profile_id -> profiles.id';
    END IF;

    -- doctors.department_id -> departments.department_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_department_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_department_id 
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: doctors.department_id -> departments.department_id';
    END IF;
END $$;

-- PATIENTS table relationships
DO $$
BEGIN
    -- patients.profile_id -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_patients_profile_id'
    ) THEN
        ALTER TABLE patients 
        ADD CONSTRAINT fk_patients_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: patients.profile_id -> profiles.id';
    END IF;
END $$;

-- APPOINTMENTS table relationships
DO $$
BEGIN
    -- appointments.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_patient_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: appointments.patient_id -> patients.patient_id';
    END IF;

    -- appointments.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_doctor_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: appointments.doctor_id -> doctors.doctor_id';
    END IF;

    -- appointments.room_id -> rooms.room_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_room_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_room_id 
        FOREIGN KEY (room_id) REFERENCES rooms(room_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: appointments.room_id -> rooms.room_id';
    END IF;
END $$;

-- MEDICAL_RECORDS table relationships
DO $$
BEGIN
    -- medical_records.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_patient_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: medical_records.patient_id -> patients.patient_id';
    END IF;

    -- medical_records.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_doctor_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: medical_records.doctor_id -> doctors.doctor_id';
    END IF;

    -- medical_records.appointment_id -> appointments.appointment_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_appointment_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_appointment_id 
        FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: medical_records.appointment_id -> appointments.appointment_id';
    END IF;
END $$;

-- Continue with other tables...
-- PRESCRIPTIONS, BILLING, PAYMENTS, SCHEDULES, AVAILABILITY, ROOMS

\echo 'Foreign key relationships added successfully!'

-- ============================================================================
-- STEP 3: ENABLE RLS AND CREATE POLICIES
-- ============================================================================

\echo 'Step 3: Setting up Row Level Security...'

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Basic policies for profiles table
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Service role full access" ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role full access" ON profiles
    FOR ALL USING (auth.role() = 'service_role');

\echo 'RLS policies created successfully!'

-- ============================================================================
-- STEP 4: ADD PERFORMANCE INDEXES
-- ============================================================================

\echo 'Step 4: Adding performance indexes...'

-- Core indexes for frequently queried columns
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email_verified ON profiles(email_verified);
CREATE INDEX IF NOT EXISTS idx_doctors_profile_id ON doctors(profile_id);
CREATE INDEX IF NOT EXISTS idx_doctors_specialization ON doctors(specialization);
CREATE INDEX IF NOT EXISTS idx_doctors_status ON doctors(status);
CREATE INDEX IF NOT EXISTS idx_patients_profile_id ON patients(profile_id);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_id ON medical_records(patient_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor_id ON medical_records(doctor_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_visit_date ON medical_records(visit_date);

\echo 'Performance indexes added successfully!'

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

\echo ''
\echo '=========================================='
\echo 'DATABASE SETUP COMPLETED SUCCESSFULLY!'
\echo '=========================================='
\echo ''
\echo 'What was configured:'
\echo '✅ Helper functions for database monitoring'
\echo '✅ Foreign key relationships between tables'
\echo '✅ Row Level Security (RLS) policies'
\echo '✅ Performance indexes'
\echo ''
\echo 'Next steps:'
\echo '1. Run the check-database-status.js script to verify'
\echo '2. Test the application functionality'
\echo '3. Monitor query performance'
\echo ''
