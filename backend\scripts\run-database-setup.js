/**
 * Hospital Management System - Database Setup Runner
 * Script để chạy toàn bộ setup database với foreign keys, RLS policies và indexes
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Supabase configuration - try both naming conventions
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSQLFile(filePath, description) {
  try {
    console.log(`\n🔄 ${description}...`);
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Split SQL content by statements (basic splitting)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('\\echo'));

    let successCount = 0;
    let errorCount = 0;

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
          
          if (error) {
            console.log(`   ⚠️  Warning: ${error.message}`);
            errorCount++;
          } else {
            successCount++;
          }
        } catch (err) {
          console.log(`   ⚠️  Warning: ${err.message}`);
          errorCount++;
        }
      }
    }

    console.log(`   ✅ Completed: ${successCount} successful, ${errorCount} warnings`);
    return { success: true, successCount, errorCount };
    
  } catch (error) {
    console.error(`   ❌ Failed to run ${filePath}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function createExecSQLFunction() {
  console.log('🔧 Creating exec_sql helper function...');
  
  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS void AS $$
    BEGIN
      EXECUTE sql_query;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
  `;

  try {
    const { error } = await supabase.rpc('exec', { sql: createFunctionSQL });
    if (error) {
      console.log('   ⚠️  Function may already exist or using alternative method');
    } else {
      console.log('   ✅ Helper function created');
    }
  } catch (err) {
    console.log('   ⚠️  Using alternative execution method');
  }
}

async function runDatabaseSetup() {
  console.log('🏥 Hospital Management System - Database Setup');
  console.log('='.repeat(60));
  
  const startTime = Date.now();
  
  try {
    // Create helper function for SQL execution
    await createExecSQLFunction();

    // Step 1: Create helper functions
    const helperFunctionsPath = path.join(__dirname, 'database-helper-functions.sql');
    if (fs.existsSync(helperFunctionsPath)) {
      await runSQLFile(helperFunctionsPath, 'Creating database helper functions');
    }

    // Step 2: Add foreign key relationships
    const foreignKeysPath = path.join(__dirname, 'add-foreign-keys.sql');
    if (fs.existsSync(foreignKeysPath)) {
      await runSQLFile(foreignKeysPath, 'Adding foreign key relationships');
    }

    // Step 3: Setup RLS policies (Part 1)
    const rlsPoliciesPath = path.join(__dirname, 'setup-rls-policies.sql');
    if (fs.existsSync(rlsPoliciesPath)) {
      await runSQLFile(rlsPoliciesPath, 'Setting up RLS policies (Part 1)');
    }

    // Step 4: Setup RLS policies (Part 2)
    const rlsPoliciesPart2Path = path.join(__dirname, 'setup-rls-policies-part2.sql');
    if (fs.existsSync(rlsPoliciesPart2Path)) {
      await runSQLFile(rlsPoliciesPart2Path, 'Setting up RLS policies (Part 2)');
    }

    // Step 5: Add performance indexes
    const indexesPath = path.join(__dirname, 'add-performance-indexes.sql');
    if (fs.existsSync(indexesPath)) {
      await runSQLFile(indexesPath, 'Adding performance indexes');
    }

    // Step 6: Run medical records service init if exists
    const medicalRecordsInitPath = path.join(__dirname, '../services/medical-records-service/database/init.sql');
    if (fs.existsSync(medicalRecordsInitPath)) {
      await runSQLFile(medicalRecordsInitPath, 'Setting up medical records extensions');
    }

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('\n' + '='.repeat(60));
    console.log('🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(60));
    console.log(`⏱️  Total time: ${duration} seconds`);
    console.log('\n📋 What was configured:');
    console.log('   ✅ Database helper functions');
    console.log('   ✅ Foreign key relationships');
    console.log('   ✅ Row Level Security (RLS) policies');
    console.log('   ✅ Performance indexes');
    console.log('   ✅ Medical records extensions');
    
    console.log('\n🔍 Next steps:');
    console.log('   1. Run: node backend/scripts/check-database-status.js');
    console.log('   2. Test your application functionality');
    console.log('   3. Monitor query performance');
    
    console.log('\n💡 Recommendations:');
    console.log('   • Backup your database regularly');
    console.log('   • Monitor RLS policy performance');
    console.log('   • Review and optimize indexes based on usage');
    console.log('   • Test all user roles (admin, doctor, patient)');

    return true;

  } catch (error) {
    console.error('\n❌ Database setup failed:', error);
    return false;
  }
}

async function runQuickCheck() {
  console.log('\n🔍 Running quick database check...');
  
  try {
    // Check if we can connect and query basic tables
    const tables = ['profiles', 'doctors', 'patients', 'appointments'];
    
    for (const table of tables) {
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
        
      if (error) {
        console.log(`   ❌ ${table}: ${error.message}`);
      } else {
        console.log(`   ✅ ${table}: ${count || 0} rows`);
      }
    }
    
    // Check if helper functions exist
    const { data: functions, error: funcError } = await supabase
      .rpc('get_foreign_keys')
      .limit(1);
      
    if (funcError) {
      console.log('   ⚠️  Helper functions may need to be created manually');
    } else {
      console.log('   ✅ Helper functions are working');
    }
    
  } catch (error) {
    console.log('   ⚠️  Quick check completed with warnings');
  }
}

// Main execution
if (require.main === module) {
  runDatabaseSetup()
    .then(async (success) => {
      if (success) {
        await runQuickCheck();
        console.log('\n✅ Setup completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Setup failed. Please check the errors above.');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('\n💥 Unexpected error:', err);
      process.exit(1);
    });
}

module.exports = { runDatabaseSetup, runQuickCheck };
