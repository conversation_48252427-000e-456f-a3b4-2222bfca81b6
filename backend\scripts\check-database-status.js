/**
 * Hospital Management System - Database Status Checker
 * Kiểm tra trạng thái database hiện tại trên Supabase
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseStatus() {
  console.log('🔍 Checking Hospital Management Database Status...\n');
  
  const results = {
    tables: {},
    foreignKeys: {},
    policies: {},
    indexes: {},
    summary: {
      totalTables: 0,
      tablesWithData: 0,
      foreignKeysCount: 0,
      policiesCount: 0,
      indexesCount: 0
    }
  };

  // Core tables to check
  const coreTables = [
    'profiles', 'doctors', 'patients', 'appointments', 
    'departments', 'rooms', 'medical_records', 'prescriptions', 
    'billing', 'payments', 'schedules', 'availability'
  ];

  // Medical record related tables
  const medicalTables = [
    'medical_record_attachments', 'lab_results', 
    'vital_signs_history', 'medical_record_templates'
  ];

  // Enum tables
  const enumTables = [
    'specialties', 'departments_enum', 'room_types', 
    'diagnosis', 'medications', 'status_values', 'payment_methods'
  ];

  const allTables = [...coreTables, ...medicalTables, ...enumTables];

  // Check each table
  for (const tableName of allTables) {
    try {
      console.log(`📋 Checking table: ${tableName}`);
      
      // Check if table exists and get row count
      const { data, error, count } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        results.tables[tableName] = {
          exists: false,
          error: error.message,
          rowCount: 0
        };
        console.log(`   ❌ Table not found or error: ${error.message}`);
      } else {
        results.tables[tableName] = {
          exists: true,
          rowCount: count || 0,
          error: null
        };
        console.log(`   ✅ Table exists with ${count || 0} rows`);
        results.summary.totalTables++;
        if (count > 0) results.summary.tablesWithData++;
      }
    } catch (err) {
      results.tables[tableName] = {
        exists: false,
        error: err.message,
        rowCount: 0
      };
      console.log(`   ❌ Error checking table: ${err.message}`);
    }
  }

  console.log('\n🔗 Checking Foreign Key Constraints...');
  await checkForeignKeys(results);

  console.log('\n🔒 Checking RLS Policies...');
  await checkRLSPolicies(results);

  console.log('\n📊 Checking Indexes...');
  await checkIndexes(results);

  // Print summary
  printSummary(results);
  
  return results;
}

async function checkForeignKeys(results) {
  try {
    const { data, error } = await supabase.rpc('get_foreign_keys');
    
    if (error) {
      console.log('   ❌ Could not retrieve foreign keys:', error.message);
      return;
    }

    if (data && data.length > 0) {
      data.forEach(fk => {
        const key = `${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`;
        results.foreignKeys[key] = fk;
        results.summary.foreignKeysCount++;
        console.log(`   ✅ ${key}`);
      });
    } else {
      console.log('   ⚠️  No foreign keys found');
    }
  } catch (err) {
    console.log('   ❌ Error checking foreign keys:', err.message);
  }
}

async function checkRLSPolicies(results) {
  try {
    const { data, error } = await supabase.rpc('get_rls_policies');
    
    if (error) {
      console.log('   ❌ Could not retrieve RLS policies:', error.message);
      return;
    }

    if (data && data.length > 0) {
      data.forEach(policy => {
        const key = `${policy.tablename}.${policy.policyname}`;
        results.policies[key] = policy;
        results.summary.policiesCount++;
        console.log(`   ✅ ${policy.tablename}: ${policy.policyname} (${policy.cmd})`);
      });
    } else {
      console.log('   ⚠️  No RLS policies found');
    }
  } catch (err) {
    console.log('   ❌ Error checking RLS policies:', err.message);
  }
}

async function checkIndexes(results) {
  try {
    const { data, error } = await supabase.rpc('get_table_indexes');
    
    if (error) {
      console.log('   ❌ Could not retrieve indexes:', error.message);
      return;
    }

    if (data && data.length > 0) {
      data.forEach(index => {
        const key = `${index.tablename}.${index.indexname}`;
        results.indexes[key] = index;
        results.summary.indexesCount++;
        console.log(`   ✅ ${index.tablename}: ${index.indexname}`);
      });
    } else {
      console.log('   ⚠️  No custom indexes found');
    }
  } catch (err) {
    console.log('   ❌ Error checking indexes:', err.message);
  }
}

function printSummary(results) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 DATABASE STATUS SUMMARY');
  console.log('='.repeat(60));
  console.log(`📋 Total Tables: ${results.summary.totalTables}`);
  console.log(`📊 Tables with Data: ${results.summary.tablesWithData}`);
  console.log(`🔗 Foreign Keys: ${results.summary.foreignKeysCount}`);
  console.log(`🔒 RLS Policies: ${results.summary.policiesCount}`);
  console.log(`📊 Indexes: ${results.summary.indexesCount}`);
  
  console.log('\n📋 MISSING TABLES:');
  Object.entries(results.tables).forEach(([table, info]) => {
    if (!info.exists) {
      console.log(`   ❌ ${table}: ${info.error}`);
    }
  });

  console.log('\n📊 EMPTY TABLES:');
  Object.entries(results.tables).forEach(([table, info]) => {
    if (info.exists && info.rowCount === 0) {
      console.log(`   ⚠️  ${table}: No data`);
    }
  });

  console.log('\n💡 RECOMMENDATIONS:');
  if (results.summary.foreignKeysCount === 0) {
    console.log('   🔗 Add foreign key constraints for data integrity');
  }
  if (results.summary.policiesCount === 0) {
    console.log('   🔒 Add RLS policies for data security');
  }
  if (results.summary.indexesCount < 10) {
    console.log('   📊 Add indexes for better performance');
  }
}

// Run the check
if (require.main === module) {
  checkDatabaseStatus()
    .then(() => {
      console.log('\n✅ Database status check completed');
      process.exit(0);
    })
    .catch(err => {
      console.error('\n❌ Database status check failed:', err);
      process.exit(1);
    });
}

module.exports = { checkDatabaseStatus };
