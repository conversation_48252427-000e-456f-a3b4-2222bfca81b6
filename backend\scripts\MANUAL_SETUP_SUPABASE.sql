-- ============================================================================
-- HOSPITAL MANAGEMENT SYSTEM - MANUAL SETUP FOR SUPABASE
-- Copy và paste script này vào Supabase SQL Editor
-- ============================================================================

-- STEP 1: CREATE HELPER FUNCTIONS
-- ============================================================================

-- Function để lấy thông tin foreign keys
CREATE OR REPLACE FUNCTION get_foreign_keys()
RETURNS TABLE (
    table_name text,
    column_name text,
    foreign_table_name text,
    foreign_column_name text,
    constraint_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tc.table_name::text,
        kcu.column_name::text,
        ccu.table_name::text AS foreign_table_name,
        ccu.column_name::text AS foreign_column_name,
        tc.constraint_name::text
    FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
    ORDER BY tc.table_name, kcu.column_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin RLS policies
CREATE OR REPLACE FUNCTION get_rls_policies()
RETURNS TABLE (
    schemaname text,
    tablename text,
    policyname text,
    permissive text,
    roles text[],
    cmd text,
    qual text,
    with_check text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.schemaname::text,
        p.tablename::text,
        p.policyname::text,
        p.permissive::text,
        p.roles,
        p.cmd::text,
        p.qual::text,
        p.with_check::text
    FROM pg_policies p
    WHERE p.schemaname = 'public'
    ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin indexes
CREATE OR REPLACE FUNCTION get_table_indexes()
RETURNS TABLE (
    schemaname text,
    tablename text,
    indexname text,
    indexdef text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.nspname::text AS schemaname,
        t.relname::text AS tablename,
        i.relname::text AS indexname,
        pg_get_indexdef(i.oid)::text AS indexdef
    FROM pg_index x
    JOIN pg_class i ON i.oid = x.indexrelid
    JOIN pg_class t ON t.oid = x.indrelid
    JOIN pg_namespace n ON n.oid = t.relnamespace
    WHERE n.nspname = 'public'
      AND NOT x.indisprimary
      AND i.relname NOT LIKE '%_pkey'
    ORDER BY t.relname, i.relname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_foreign_keys() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_rls_policies() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_table_indexes() TO authenticated, anon;

-- STEP 2: ADD FOREIGN KEY RELATIONSHIPS
-- ============================================================================

-- DOCTORS table relationships
DO $$
BEGIN
    -- doctors.profile_id -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_profile_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        -- Added: doctors.profile_id -> profiles.id
    END IF;

    -- doctors.department_id -> departments.department_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_department_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_department_id 
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: doctors.department_id -> departments.department_id';
    END IF;
END $$;

-- PATIENTS table relationships
DO $$
BEGIN
    -- patients.profile_id -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_patients_profile_id'
    ) THEN
        ALTER TABLE patients 
        ADD CONSTRAINT fk_patients_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: patients.profile_id -> profiles.id';
    END IF;
END $$;

-- APPOINTMENTS table relationships
DO $$
BEGIN
    -- appointments.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_patient_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: appointments.patient_id -> patients.patient_id';
    END IF;

    -- appointments.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_doctor_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: appointments.doctor_id -> doctors.doctor_id';
    END IF;

    -- appointments.room_id -> rooms.room_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_room_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_room_id 
        FOREIGN KEY (room_id) REFERENCES rooms(room_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: appointments.room_id -> rooms.room_id';
    END IF;
END $$;

-- MEDICAL_RECORDS table relationships
DO $$
BEGIN
    -- medical_records.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_patient_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: medical_records.patient_id -> patients.patient_id';
    END IF;

    -- medical_records.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_doctor_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: medical_records.doctor_id -> doctors.doctor_id';
    END IF;

    -- medical_records.appointment_id -> appointments.appointment_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_appointment_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_appointment_id 
        FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: medical_records.appointment_id -> appointments.appointment_id';
    END IF;
END $$;

-- PRESCRIPTIONS table relationships
DO $$
BEGIN
    -- prescriptions.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_patient_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: prescriptions.patient_id -> patients.patient_id';
    END IF;

    -- prescriptions.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_doctor_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: prescriptions.doctor_id -> doctors.doctor_id';
    END IF;

    -- prescriptions.medical_record_id -> medical_records.record_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_medical_record_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_medical_record_id 
        FOREIGN KEY (medical_record_id) REFERENCES medical_records(record_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: prescriptions.medical_record_id -> medical_records.record_id';
    END IF;
END $$;

-- BILLING table relationships
DO $$
BEGIN
    -- billing.patient_id -> patients.patient_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_billing_patient_id'
    ) THEN
        ALTER TABLE billing 
        ADD CONSTRAINT fk_billing_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: billing.patient_id -> patients.patient_id';
    END IF;

    -- billing.appointment_id -> appointments.appointment_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_billing_appointment_id'
    ) THEN
        ALTER TABLE billing 
        ADD CONSTRAINT fk_billing_appointment_id 
        FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: billing.appointment_id -> appointments.appointment_id';
    END IF;
END $$;

-- PAYMENTS table relationships
DO $$
BEGIN
    -- payments.bill_id -> billing.bill_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_payments_bill_id'
    ) THEN
        ALTER TABLE payments 
        ADD CONSTRAINT fk_payments_bill_id 
        FOREIGN KEY (bill_id) REFERENCES billing(bill_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: payments.bill_id -> billing.bill_id';
    END IF;
END $$;

-- SCHEDULES table relationships
DO $$
BEGIN
    -- schedules.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_schedules_doctor_id'
    ) THEN
        ALTER TABLE schedules 
        ADD CONSTRAINT fk_schedules_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: schedules.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- AVAILABILITY table relationships
DO $$
BEGIN
    -- availability.doctor_id -> doctors.doctor_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_availability_doctor_id'
    ) THEN
        ALTER TABLE availability 
        ADD CONSTRAINT fk_availability_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added: availability.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- ROOMS table relationships
DO $$
BEGIN
    -- rooms.department_id -> departments.department_id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_rooms_department_id'
    ) THEN
        ALTER TABLE rooms 
        ADD CONSTRAINT fk_rooms_department_id 
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added: rooms.department_id -> departments.department_id';
    END IF;
END $$;

RAISE NOTICE 'Foreign key relationships setup completed!';

-- STEP 3: ENABLE RLS AND CREATE POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Enable RLS on medical record related tables
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_attachments') THEN
        ALTER TABLE medical_record_attachments ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'lab_results') THEN
        ALTER TABLE lab_results ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vital_signs_history') THEN
        ALTER TABLE vital_signs_history ENABLE ROW LEVEL SECURITY;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_templates') THEN
        ALTER TABLE medical_record_templates ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- PROFILES TABLE POLICIES
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Service role full access" ON profiles;

CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can manage all profiles" ON profiles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role full access" ON profiles
    FOR ALL USING (auth.role() = 'service_role');

-- DOCTORS TABLE POLICIES
DROP POLICY IF EXISTS "Doctors can view own data" ON doctors;
DROP POLICY IF EXISTS "Doctors can update own data" ON doctors;
DROP POLICY IF EXISTS "Admins can manage doctors" ON doctors;
DROP POLICY IF EXISTS "Patients can view doctor info" ON doctors;
DROP POLICY IF EXISTS "Service role full access doctors" ON doctors;

CREATE POLICY "Doctors can view own data" ON doctors
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Doctors can update own data" ON doctors
    FOR UPDATE USING (profile_id = auth.uid());

CREATE POLICY "Admins can manage doctors" ON doctors
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Patients can view doctor info" ON doctors
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

CREATE POLICY "Service role full access doctors" ON doctors
    FOR ALL USING (auth.role() = 'service_role');

-- PATIENTS TABLE POLICIES
DROP POLICY IF EXISTS "Patients can view own data" ON patients;
DROP POLICY IF EXISTS "Patients can update own data" ON patients;
DROP POLICY IF EXISTS "Doctors can view patient data" ON patients;
DROP POLICY IF EXISTS "Admins can manage patients" ON patients;
DROP POLICY IF EXISTS "Service role full access patients" ON patients;

CREATE POLICY "Patients can view own data" ON patients
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Patients can update own data" ON patients
    FOR UPDATE USING (profile_id = auth.uid());

CREATE POLICY "Doctors can view patient data" ON patients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

CREATE POLICY "Admins can manage patients" ON patients
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role full access patients" ON patients
    FOR ALL USING (auth.role() = 'service_role');

-- APPOINTMENTS TABLE POLICIES
DROP POLICY IF EXISTS "Users can view own appointments" ON appointments;
DROP POLICY IF EXISTS "Patients can manage own appointments" ON appointments;
DROP POLICY IF EXISTS "Doctors can view their appointments" ON appointments;
DROP POLICY IF EXISTS "Doctors can update their appointments" ON appointments;
DROP POLICY IF EXISTS "Admins can manage all appointments" ON appointments;
DROP POLICY IF EXISTS "Service role full access appointments" ON appointments;

CREATE POLICY "Users can view own appointments" ON appointments
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
        OR doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

CREATE POLICY "Patients can manage own appointments" ON appointments
    FOR ALL USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
        AND EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

CREATE POLICY "Doctors can view their appointments" ON appointments
    FOR SELECT USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

CREATE POLICY "Doctors can update their appointments" ON appointments
    FOR UPDATE USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

CREATE POLICY "Admins can manage all appointments" ON appointments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role full access appointments" ON appointments
    FOR ALL USING (auth.role() = 'service_role');

-- MEDICAL RECORDS TABLE POLICIES
DROP POLICY IF EXISTS "Patients can view own medical records" ON medical_records;
DROP POLICY IF EXISTS "Doctors can view patient medical records" ON medical_records;
DROP POLICY IF EXISTS "Doctors can manage medical records" ON medical_records;
DROP POLICY IF EXISTS "Admins can manage all medical records" ON medical_records;
DROP POLICY IF EXISTS "Service role full access medical records" ON medical_records;

CREATE POLICY "Patients can view own medical records" ON medical_records
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
    );

CREATE POLICY "Doctors can view patient medical records" ON medical_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

CREATE POLICY "Doctors can manage medical records" ON medical_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

CREATE POLICY "Admins can manage all medical records" ON medical_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role full access medical records" ON medical_records
    FOR ALL USING (auth.role() = 'service_role');

RAISE NOTICE 'RLS policies setup completed!';

-- STEP 4: ADD PERFORMANCE INDEXES
-- ============================================================================

-- PROFILES TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email_verified ON profiles(email_verified);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- DOCTORS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_doctors_profile_id ON doctors(profile_id);
CREATE INDEX IF NOT EXISTS idx_doctors_department_id ON doctors(department_id);
CREATE INDEX IF NOT EXISTS idx_doctors_specialization ON doctors(specialization);
CREATE INDEX IF NOT EXISTS idx_doctors_status ON doctors(status);
CREATE INDEX IF NOT EXISTS idx_doctors_license_number ON doctors(license_number);
CREATE INDEX IF NOT EXISTS idx_doctors_consultation_fee ON doctors(consultation_fee);
CREATE INDEX IF NOT EXISTS idx_doctors_status_specialization ON doctors(status, specialization);

-- PATIENTS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_patients_profile_id ON patients(profile_id);
CREATE INDEX IF NOT EXISTS idx_patients_date_of_birth ON patients(date_of_birth);
CREATE INDEX IF NOT EXISTS idx_patients_gender ON patients(gender);
CREATE INDEX IF NOT EXISTS idx_patients_blood_type ON patients(blood_type);
CREATE INDEX IF NOT EXISTS idx_patients_emergency_contact_phone ON patients USING GIN ((emergency_contact->>'phone'));
CREATE INDEX IF NOT EXISTS idx_patients_insurance_provider ON patients USING GIN ((insurance_info->>'provider'));

-- APPOINTMENTS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON appointments(doctor_id);
CREATE INDEX IF NOT EXISTS idx_appointments_room_id ON appointments(room_id);
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_time ON appointments(appointment_time);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_type ON appointments(appointment_type);
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_date ON appointments(patient_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status_date ON appointments(status, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_created_at ON appointments(created_at);

-- MEDICAL RECORDS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_id ON medical_records(patient_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor_id ON medical_records(doctor_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_appointment_id ON medical_records(appointment_id);
CREATE INDEX IF NOT EXISTS idx_medical_records_visit_date ON medical_records(visit_date);
CREATE INDEX IF NOT EXISTS idx_medical_records_status ON medical_records(status);
CREATE INDEX IF NOT EXISTS idx_medical_records_diagnosis_text ON medical_records USING GIN (to_tsvector('english', diagnosis));
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_visit_date ON medical_records(patient_id, visit_date);
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor_visit_date ON medical_records(doctor_id, visit_date);

-- PRESCRIPTIONS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient_id ON prescriptions(patient_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_doctor_id ON prescriptions(doctor_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_medical_record_id ON prescriptions(medical_record_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_prescription_date ON prescriptions(prescription_date);
CREATE INDEX IF NOT EXISTS idx_prescriptions_status ON prescriptions(status);
CREATE INDEX IF NOT EXISTS idx_prescriptions_medications ON prescriptions USING GIN (medications);
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient_date ON prescriptions(patient_id, prescription_date);
CREATE INDEX IF NOT EXISTS idx_prescriptions_status_date ON prescriptions(status, prescription_date);

-- BILLING TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_billing_patient_id ON billing(patient_id);
CREATE INDEX IF NOT EXISTS idx_billing_appointment_id ON billing(appointment_id);
CREATE INDEX IF NOT EXISTS idx_billing_bill_date ON billing(bill_date);
CREATE INDEX IF NOT EXISTS idx_billing_due_date ON billing(due_date);
CREATE INDEX IF NOT EXISTS idx_billing_status ON billing(status);
CREATE INDEX IF NOT EXISTS idx_billing_total_amount ON billing(total_amount);
CREATE INDEX IF NOT EXISTS idx_billing_patient_status ON billing(patient_id, status);
CREATE INDEX IF NOT EXISTS idx_billing_status_due_date ON billing(status, due_date);

-- PAYMENTS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_payments_bill_id ON payments(bill_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_payment_method ON payments(payment_method);
CREATE INDEX IF NOT EXISTS idx_payments_amount ON payments(amount);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_bill_date ON payments(bill_id, payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_method_date ON payments(payment_method, payment_date);

-- SCHEDULES TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_id ON schedules(doctor_id);
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_start_date ON schedules(start_date);
CREATE INDEX IF NOT EXISTS idx_schedules_end_date ON schedules(end_date);
CREATE INDEX IF NOT EXISTS idx_schedules_is_available ON schedules(is_available);
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_day ON schedules(doctor_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_available ON schedules(doctor_id, is_available);

-- AVAILABILITY TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_availability_doctor_id ON availability(doctor_id);
CREATE INDEX IF NOT EXISTS idx_availability_date ON availability(date);
CREATE INDEX IF NOT EXISTS idx_availability_is_available ON availability(is_available);
CREATE INDEX IF NOT EXISTS idx_availability_doctor_date ON availability(doctor_id, date);
CREATE INDEX IF NOT EXISTS idx_availability_date_available ON availability(date, is_available);

-- DEPARTMENTS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);
CREATE INDEX IF NOT EXISTS idx_departments_status ON departments(status);
CREATE INDEX IF NOT EXISTS idx_departments_head_doctor_id ON departments(head_doctor_id);

-- ROOMS TABLE INDEXES
CREATE INDEX IF NOT EXISTS idx_rooms_department_id ON rooms(department_id);
CREATE INDEX IF NOT EXISTS idx_rooms_room_number ON rooms(room_number);
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_capacity ON rooms(capacity);
CREATE INDEX IF NOT EXISTS idx_rooms_department_type ON rooms(department_id, room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_status_type ON rooms(status, room_type);

RAISE NOTICE 'Performance indexes created successfully!';

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

RAISE NOTICE '';
RAISE NOTICE '==========================================';
RAISE NOTICE 'DATABASE SETUP COMPLETED SUCCESSFULLY!';
RAISE NOTICE '==========================================';
RAISE NOTICE '';
RAISE NOTICE 'What was configured:';
RAISE NOTICE '✅ Helper functions for database monitoring';
RAISE NOTICE '✅ Foreign key relationships between tables';
RAISE NOTICE '✅ Row Level Security (RLS) policies';
RAISE NOTICE '✅ Performance indexes';
RAISE NOTICE '';
RAISE NOTICE 'Next steps:';
RAISE NOTICE '1. Run: npm run db:check';
RAISE NOTICE '2. Test the application functionality';
RAISE NOTICE '3. Monitor query performance';
RAISE NOTICE '';
