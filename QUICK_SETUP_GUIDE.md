# 🚀 Quick Setup Guide - Database Relationships & Security

## 📋 Tình hình hiện tại

✅ **Database đã có**: 23 bảng với 8 bảng có dữ liệu  
❌ **Thiếu**: Foreign keys, RLS policies, Performance indexes

## 🎯 Cách thực hiện nhanh nhất

### Bước 1: Mở Supabase Dashboard
1. Vào [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Chọn project: `ciasxktujslgsdgylimv`
3. Vào **SQL Editor** (menu bên trái)

### Bước 2: Copy & Paste Script
1. Mở file: `backend/scripts/SUPABASE_SETUP_FIXED.sql` ⭐ **SỬ DỤNG FILE NÀY**
2. **Copy toàn bộ nội dung** (685 dòng)
3. **Paste vào Supabase SQL Editor**
4. **Click "Run"** ▶️

> ⚠️ **Lưu ý**: Sử dụng file `SUPABASE_SETUP_FIXED.sql` thay vì `MANUAL_SETUP_SUPABASE.sql` vì đã sửa lỗi syntax.

### Bước 3: <PERSON><PERSON>m tra kết quả
```bash
cd backend
npm run db:check
```

## 📊 Kết quả mong đợi

Sau khi chạy script, bạn sẽ thấy:

```
📊 DATABASE STATUS SUMMARY
==========================================
📋 Total Tables: 23
📊 Tables with Data: 8
🔗 Foreign Keys: 20+
🔒 RLS Policies: 50+
📊 Indexes: 30+
```

## 🔗 Foreign Keys sẽ được thêm

- `doctors.profile_id` → `profiles.id`
- `doctors.department_id` → `departments.department_id`
- `patients.profile_id` → `profiles.id`
- `appointments.patient_id` → `patients.patient_id`
- `appointments.doctor_id` → `doctors.doctor_id`
- `appointments.room_id` → `rooms.room_id`
- `medical_records.patient_id` → `patients.patient_id`
- `medical_records.doctor_id` → `doctors.doctor_id`
- `medical_records.appointment_id` → `appointments.appointment_id`
- `prescriptions.patient_id` → `patients.patient_id`
- `prescriptions.doctor_id` → `doctors.doctor_id`
- `prescriptions.medical_record_id` → `medical_records.record_id`
- `billing.patient_id` → `patients.patient_id`
- `billing.appointment_id` → `appointments.appointment_id`
- `payments.bill_id` → `billing.bill_id`
- `schedules.doctor_id` → `doctors.doctor_id`
- `availability.doctor_id` → `doctors.doctor_id`
- `rooms.department_id` → `departments.department_id`

## 🔒 RLS Policies sẽ được thiết lập

### User Roles
- **Admin**: Full access to all data
- **Doctor**: Access to their patients' data and own data
- **Patient**: Access to own data only

### Key Policies
- **Profiles**: Users can view/update own profile, admins can manage all
- **Doctors**: Doctors can manage own data, patients can view doctor info
- **Patients**: Patients can manage own data, doctors can view patient data
- **Appointments**: Users can view own appointments, role-based management
- **Medical Records**: Patients can view own, doctors can manage
- **Prescriptions**: Patients can view own, doctors can manage
- **Billing/Payments**: Patients can view own, admins can manage

## 📊 Performance Indexes sẽ được thêm

### Core Indexes
- **profiles**: role, email_verified, is_active
- **doctors**: profile_id, specialization, status, department_id
- **patients**: profile_id, date_of_birth, gender
- **appointments**: patient_id, doctor_id, appointment_date, status
- **medical_records**: patient_id, doctor_id, visit_date
- **prescriptions**: patient_id, doctor_id, prescription_date
- **billing**: patient_id, status, due_date
- **payments**: bill_id, payment_date, payment_method

### Composite Indexes
- `appointments(doctor_id, appointment_date)`
- `medical_records(patient_id, visit_date)`
- `billing(patient_id, status)`
- `prescriptions(patient_id, prescription_date)`

## 🛠️ Helper Functions sẽ được tạo

- `get_foreign_keys()` - Lấy danh sách foreign keys
- `get_rls_policies()` - Lấy danh sách RLS policies
- `get_table_indexes()` - Lấy danh sách indexes

## ⚠️ Lưu ý quan trọng

1. **Backup database** trước khi chạy (khuyến nghị)
2. Script **an toàn** - có thể chạy nhiều lần
3. Sử dụng `IF NOT EXISTS` để tránh lỗi duplicate
4. **Không xóa dữ liệu** hiện có

## 🔍 Kiểm tra sau khi setup

### Test Helper Functions
```sql
-- Trong Supabase SQL Editor
SELECT * FROM get_foreign_keys();
SELECT * FROM get_rls_policies();
SELECT * FROM get_table_indexes();
```

### Test Application
1. Login với các roles khác nhau (admin, doctor, patient)
2. Kiểm tra permissions hoạt động đúng
3. Test performance của queries

## 📞 Nếu gặp lỗi

### Lỗi Foreign Key
- Kiểm tra dữ liệu có tồn tại trong bảng reference
- Xóa dữ liệu không hợp lệ trước

### Lỗi RLS Policy
- Kiểm tra auth context
- Verify user role trong profiles table

### Lỗi Index
- Kiểm tra column có tồn tại
- Review data types

---

## 🎉 Sẵn sàng!

**Chỉ cần 3 bước:**
1. Mở Supabase SQL Editor
2. Copy & paste `MANUAL_SETUP_SUPABASE.sql`
3. Click Run ▶️

**Thời gian**: ~2-3 phút  
**Kết quả**: Database hoàn chỉnh với relationships, security & performance!
