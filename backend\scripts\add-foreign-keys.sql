-- Hospital Management System - Foreign Key Relationships
-- Thê<PERSON> các mối liên hệ foreign key gi<PERSON>a các bảng

-- ============================================================================
-- CORE TABLE RELATIONSHIPS
-- ============================================================================

-- 1. PROFILES table relationships
-- profiles.id references auth.users.id (already handled by <PERSON>pabase Auth)

-- 2. DOCTORS table relationships
-- doctors.profile_id -> profiles.id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_profile_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: doctors.profile_id -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: doctors.profile_id -> profiles.id';
    END IF;
END $$;

-- doctors.department_id -> departments.department_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_doctors_department_id'
    ) THEN
        ALTER TABLE doctors 
        ADD CONSTRAINT fk_doctors_department_id 
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: doctors.department_id -> departments.department_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: doctors.department_id -> departments.department_id';
    END IF;
END $$;

-- 3. PATIENTS table relationships
-- patients.profile_id -> profiles.id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_patients_profile_id'
    ) THEN
        ALTER TABLE patients 
        ADD CONSTRAINT fk_patients_profile_id 
        FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: patients.profile_id -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: patients.profile_id -> profiles.id';
    END IF;
END $$;

-- 4. APPOINTMENTS table relationships
-- appointments.patient_id -> patients.patient_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_patient_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: appointments.patient_id -> patients.patient_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: appointments.patient_id -> patients.patient_id';
    END IF;
END $$;

-- appointments.doctor_id -> doctors.doctor_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_doctor_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: appointments.doctor_id -> doctors.doctor_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: appointments.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- appointments.room_id -> rooms.room_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_room_id'
    ) THEN
        ALTER TABLE appointments 
        ADD CONSTRAINT fk_appointments_room_id 
        FOREIGN KEY (room_id) REFERENCES rooms(room_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: appointments.room_id -> rooms.room_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: appointments.room_id -> rooms.room_id';
    END IF;
END $$;

-- 5. MEDICAL_RECORDS table relationships
-- medical_records.patient_id -> patients.patient_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_patient_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: medical_records.patient_id -> patients.patient_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_records.patient_id -> patients.patient_id';
    END IF;
END $$;

-- medical_records.doctor_id -> doctors.doctor_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_doctor_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: medical_records.doctor_id -> doctors.doctor_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_records.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- medical_records.appointment_id -> appointments.appointment_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_medical_records_appointment_id'
    ) THEN
        ALTER TABLE medical_records 
        ADD CONSTRAINT fk_medical_records_appointment_id 
        FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: medical_records.appointment_id -> appointments.appointment_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_records.appointment_id -> appointments.appointment_id';
    END IF;
END $$;

-- 6. PRESCRIPTIONS table relationships
-- prescriptions.patient_id -> patients.patient_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_patient_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: prescriptions.patient_id -> patients.patient_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: prescriptions.patient_id -> patients.patient_id';
    END IF;
END $$;

-- prescriptions.doctor_id -> doctors.doctor_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_doctor_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: prescriptions.doctor_id -> doctors.doctor_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: prescriptions.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- prescriptions.medical_record_id -> medical_records.record_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_prescriptions_medical_record_id'
    ) THEN
        ALTER TABLE prescriptions 
        ADD CONSTRAINT fk_prescriptions_medical_record_id 
        FOREIGN KEY (medical_record_id) REFERENCES medical_records(record_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: prescriptions.medical_record_id -> medical_records.record_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: prescriptions.medical_record_id -> medical_records.record_id';
    END IF;
END $$;

-- 7. BILLING table relationships
-- billing.patient_id -> patients.patient_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_billing_patient_id'
    ) THEN
        ALTER TABLE billing 
        ADD CONSTRAINT fk_billing_patient_id 
        FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: billing.patient_id -> patients.patient_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: billing.patient_id -> patients.patient_id';
    END IF;
END $$;

-- billing.appointment_id -> appointments.appointment_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_billing_appointment_id'
    ) THEN
        ALTER TABLE billing 
        ADD CONSTRAINT fk_billing_appointment_id 
        FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: billing.appointment_id -> appointments.appointment_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: billing.appointment_id -> appointments.appointment_id';
    END IF;
END $$;

-- 8. PAYMENTS table relationships
-- payments.bill_id -> billing.bill_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_payments_bill_id'
    ) THEN
        ALTER TABLE payments 
        ADD CONSTRAINT fk_payments_bill_id 
        FOREIGN KEY (bill_id) REFERENCES billing(bill_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: payments.bill_id -> billing.bill_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: payments.bill_id -> billing.bill_id';
    END IF;
END $$;

-- 9. SCHEDULES table relationships
-- schedules.doctor_id -> doctors.doctor_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_schedules_doctor_id'
    ) THEN
        ALTER TABLE schedules 
        ADD CONSTRAINT fk_schedules_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: schedules.doctor_id -> doctors.doctor_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: schedules.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- 10. AVAILABILITY table relationships
-- availability.doctor_id -> doctors.doctor_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_availability_doctor_id'
    ) THEN
        ALTER TABLE availability 
        ADD CONSTRAINT fk_availability_doctor_id 
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key: availability.doctor_id -> doctors.doctor_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: availability.doctor_id -> doctors.doctor_id';
    END IF;
END $$;

-- 11. ROOMS table relationships
-- rooms.department_id -> departments.department_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_rooms_department_id'
    ) THEN
        ALTER TABLE rooms 
        ADD CONSTRAINT fk_rooms_department_id 
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key: rooms.department_id -> departments.department_id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: rooms.department_id -> departments.department_id';
    END IF;
END $$;

-- ============================================================================
-- MEDICAL RECORD RELATED TABLE RELATIONSHIPS
-- ============================================================================

-- Medical Record Attachments (already defined in medical-records-service/database/init.sql)
-- medical_record_attachments.record_id -> medical_records.record_id

-- Lab Results (already defined in medical-records-service/database/init.sql)
-- lab_results.record_id -> medical_records.record_id

-- Vital Signs History (already defined in medical-records-service/database/init.sql)
-- vital_signs_history.record_id -> medical_records.record_id

-- Medical Record Templates - Add foreign key for created_by
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_medical_record_templates_created_by'
    ) THEN
        -- First, add the constraint if the column references a valid table
        -- Assuming created_by references doctors.doctor_id or profiles.id
        -- We'll use profiles.id for more flexibility
        ALTER TABLE medical_record_templates
        ADD CONSTRAINT fk_medical_record_templates_created_by
        FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;

        RAISE NOTICE 'Added foreign key: medical_record_templates.created_by -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_record_templates.created_by -> profiles.id';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not add foreign key for medical_record_templates.created_by: %', SQLERRM;
END $$;

-- ============================================================================
-- ADDITIONAL RELATIONSHIPS FOR AUDIT FIELDS
-- ============================================================================

-- Add foreign keys for created_by and updated_by fields in medical_records
DO $$
BEGIN
    -- created_by -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_medical_records_created_by'
    ) THEN
        ALTER TABLE medical_records
        ADD CONSTRAINT fk_medical_records_created_by
        FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;

        RAISE NOTICE 'Added foreign key: medical_records.created_by -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_records.created_by -> profiles.id';
    END IF;

    -- updated_by -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_medical_records_updated_by'
    ) THEN
        ALTER TABLE medical_records
        ADD CONSTRAINT fk_medical_records_updated_by
        FOREIGN KEY (updated_by) REFERENCES profiles(id) ON DELETE SET NULL;

        RAISE NOTICE 'Added foreign key: medical_records.updated_by -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_records.updated_by -> profiles.id';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not add audit foreign keys for medical_records: %', SQLERRM;
END $$;

-- Add foreign keys for uploaded_by and recorded_by fields
DO $$
BEGIN
    -- medical_record_attachments.uploaded_by -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_medical_record_attachments_uploaded_by'
    ) THEN
        ALTER TABLE medical_record_attachments
        ADD CONSTRAINT fk_medical_record_attachments_uploaded_by
        FOREIGN KEY (uploaded_by) REFERENCES profiles(id) ON DELETE SET NULL;

        RAISE NOTICE 'Added foreign key: medical_record_attachments.uploaded_by -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: medical_record_attachments.uploaded_by -> profiles.id';
    END IF;

    -- vital_signs_history.recorded_by -> profiles.id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_vital_signs_history_recorded_by'
    ) THEN
        ALTER TABLE vital_signs_history
        ADD CONSTRAINT fk_vital_signs_history_recorded_by
        FOREIGN KEY (recorded_by) REFERENCES profiles(id) ON DELETE SET NULL;

        RAISE NOTICE 'Added foreign key: vital_signs_history.recorded_by -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key already exists: vital_signs_history.recorded_by -> profiles.id';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Could not add audit foreign keys for related tables: %', SQLERRM;
END $$;

RAISE NOTICE 'All foreign key relationships setup completed!';
