-- Hospital Management System - Performance Indexes
-- Thêm các indexes để tối ưu hiệu suất truy vấn

-- ============================================================================
-- PROFILES TABLE INDEXES
-- ============================================================================

-- Index for email lookups (unique constraint already creates index)
-- Index for role-based queries
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email_verified ON profiles(email_verified);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- ============================================================================
-- DOCTORS TABLE INDEXES
-- ============================================================================

-- Index for profile_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_doctors_profile_id ON doctors(profile_id);
-- Index for department_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_doctors_department_id ON doctors(department_id);
-- Index for specialization searches
CREATE INDEX IF NOT EXISTS idx_doctors_specialization ON doctors(specialization);
-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_doctors_status ON doctors(status);
-- Index for license number (unique searches)
CREATE INDEX IF NOT EXISTS idx_doctors_license_number ON doctors(license_number);
-- Index for consultation fee range queries
CREATE INDEX IF NOT EXISTS idx_doctors_consultation_fee ON doctors(consultation_fee);
-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_doctors_status_specialization ON doctors(status, specialization);

-- ============================================================================
-- PATIENTS TABLE INDEXES
-- ============================================================================

-- Index for profile_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_patients_profile_id ON patients(profile_id);
-- Index for date of birth (age calculations)
CREATE INDEX IF NOT EXISTS idx_patients_date_of_birth ON patients(date_of_birth);
-- Index for gender filtering
CREATE INDEX IF NOT EXISTS idx_patients_gender ON patients(gender);
-- Index for blood type
CREATE INDEX IF NOT EXISTS idx_patients_blood_type ON patients(blood_type);
-- Index for emergency contact searches (JSONB)
CREATE INDEX IF NOT EXISTS idx_patients_emergency_contact_phone ON patients USING GIN ((emergency_contact->>'phone'));
-- Index for insurance provider (JSONB)
CREATE INDEX IF NOT EXISTS idx_patients_insurance_provider ON patients USING GIN ((insurance_info->>'provider'));

-- ============================================================================
-- APPOINTMENTS TABLE INDEXES
-- ============================================================================

-- Index for patient_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON appointments(patient_id);
-- Index for doctor_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON appointments(doctor_id);
-- Index for room_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_appointments_room_id ON appointments(room_id);
-- Index for appointment date/time
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_date ON appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_time ON appointments(appointment_time);
-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
-- Index for appointment type
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_type ON appointments(appointment_type);
-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_date ON appointments(patient_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_status_date ON appointments(status, appointment_date);
-- Index for created_at (recent appointments)
CREATE INDEX IF NOT EXISTS idx_appointments_created_at ON appointments(created_at);

-- ============================================================================
-- MEDICAL RECORDS TABLE INDEXES
-- ============================================================================

-- Index for patient_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_id ON medical_records(patient_id);
-- Index for doctor_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor_id ON medical_records(doctor_id);
-- Index for appointment_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_medical_records_appointment_id ON medical_records(appointment_id);
-- Index for visit date
CREATE INDEX IF NOT EXISTS idx_medical_records_visit_date ON medical_records(visit_date);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_medical_records_status ON medical_records(status);
-- Index for diagnosis searches (text search)
CREATE INDEX IF NOT EXISTS idx_medical_records_diagnosis_text ON medical_records USING GIN (to_tsvector('english', diagnosis));
-- Index for created_by and updated_by
CREATE INDEX IF NOT EXISTS idx_medical_records_created_by ON medical_records(created_by);
CREATE INDEX IF NOT EXISTS idx_medical_records_updated_by ON medical_records(updated_by);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_medical_records_patient_visit_date ON medical_records(patient_id, visit_date);
CREATE INDEX IF NOT EXISTS idx_medical_records_doctor_visit_date ON medical_records(doctor_id, visit_date);

-- ============================================================================
-- PRESCRIPTIONS TABLE INDEXES
-- ============================================================================

-- Index for patient_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient_id ON prescriptions(patient_id);
-- Index for doctor_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_prescriptions_doctor_id ON prescriptions(doctor_id);
-- Index for medical_record_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_prescriptions_medical_record_id ON prescriptions(medical_record_id);
-- Index for prescription date
CREATE INDEX IF NOT EXISTS idx_prescriptions_prescription_date ON prescriptions(prescription_date);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_prescriptions_status ON prescriptions(status);
-- Index for medication searches (JSONB array)
CREATE INDEX IF NOT EXISTS idx_prescriptions_medications ON prescriptions USING GIN (medications);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient_date ON prescriptions(patient_id, prescription_date);
CREATE INDEX IF NOT EXISTS idx_prescriptions_status_date ON prescriptions(status, prescription_date);

-- ============================================================================
-- BILLING TABLE INDEXES
-- ============================================================================

-- Index for patient_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_billing_patient_id ON billing(patient_id);
-- Index for appointment_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_billing_appointment_id ON billing(appointment_id);
-- Index for bill date
CREATE INDEX IF NOT EXISTS idx_billing_bill_date ON billing(bill_date);
-- Index for due date
CREATE INDEX IF NOT EXISTS idx_billing_due_date ON billing(due_date);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_billing_status ON billing(status);
-- Index for total amount (range queries)
CREATE INDEX IF NOT EXISTS idx_billing_total_amount ON billing(total_amount);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_billing_patient_status ON billing(patient_id, status);
CREATE INDEX IF NOT EXISTS idx_billing_status_due_date ON billing(status, due_date);

-- ============================================================================
-- PAYMENTS TABLE INDEXES
-- ============================================================================

-- Index for bill_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_payments_bill_id ON payments(bill_id);
-- Index for payment date
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);
-- Index for payment method
CREATE INDEX IF NOT EXISTS idx_payments_payment_method ON payments(payment_method);
-- Index for amount
CREATE INDEX IF NOT EXISTS idx_payments_amount ON payments(amount);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_payments_bill_date ON payments(bill_id, payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_method_date ON payments(payment_method, payment_date);

-- ============================================================================
-- SCHEDULES TABLE INDEXES
-- ============================================================================

-- Index for doctor_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_id ON schedules(doctor_id);
-- Index for day of week
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
-- Index for date range
CREATE INDEX IF NOT EXISTS idx_schedules_start_date ON schedules(start_date);
CREATE INDEX IF NOT EXISTS idx_schedules_end_date ON schedules(end_date);
-- Index for is_available
CREATE INDEX IF NOT EXISTS idx_schedules_is_available ON schedules(is_available);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_day ON schedules(doctor_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_available ON schedules(doctor_id, is_available);

-- ============================================================================
-- AVAILABILITY TABLE INDEXES
-- ============================================================================

-- Index for doctor_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_availability_doctor_id ON availability(doctor_id);
-- Index for date
CREATE INDEX IF NOT EXISTS idx_availability_date ON availability(date);
-- Index for is_available
CREATE INDEX IF NOT EXISTS idx_availability_is_available ON availability(is_available);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_availability_doctor_date ON availability(doctor_id, date);
CREATE INDEX IF NOT EXISTS idx_availability_date_available ON availability(date, is_available);

-- ============================================================================
-- DEPARTMENTS TABLE INDEXES
-- ============================================================================

-- Index for department name
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_departments_status ON departments(status);
-- Index for head_doctor_id
CREATE INDEX IF NOT EXISTS idx_departments_head_doctor_id ON departments(head_doctor_id);

-- ============================================================================
-- ROOMS TABLE INDEXES
-- ============================================================================

-- Index for department_id (foreign key)
CREATE INDEX IF NOT EXISTS idx_rooms_department_id ON rooms(department_id);
-- Index for room number
CREATE INDEX IF NOT EXISTS idx_rooms_room_number ON rooms(room_number);
-- Index for room type
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON rooms(room_type);
-- Index for status
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
-- Index for capacity
CREATE INDEX IF NOT EXISTS idx_rooms_capacity ON rooms(capacity);
-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_rooms_department_type ON rooms(department_id, room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_status_type ON rooms(status, room_type);

RAISE NOTICE 'Performance indexes created successfully!';
