{"name": "hospital-microservices", "version": "1.0.0", "description": "Hospital Management System with Microservices Architecture", "main": "index.js", "scripts": {"dev": "node scripts/start-all-microservices.js", "dev:core": "concurrently \"npm run dev:gateway\" \"npm run dev:doctor\" \"npm run dev:patient\" \"npm run dev:appointment\"", "dev:gateway": "cd api-gateway && npm run dev", "dev:doctor": "cd services/doctor-service && npm run dev", "dev:patient": "cd services/patient-service && npm run dev", "dev:appointment": "cd services/appointment-service && npm run dev", "dev:medical-records": "cd services/medical-records-service && npm run dev", "dev:prescription": "cd services/prescription-service && npm run dev", "dev:billing": "cd services/billing-service && npm run dev", "dev:room": "cd services/room-service && npm run dev", "dev:notification": "cd services/notification-service && npm run dev", "dev:file-storage": "cd services/file-storage-service && npm run dev", "dev:audit": "cd services/audit-service && npm run dev", "dev:chatbot": "cd services/chatbot-service && npm run dev", "dev:frontend": "cd frontend && npm run dev", "setup:tables": "node scripts/setup-microservices-tables.js", "setup:env": "node scripts/setup-environment.js", "build": "npm run build:gateway && npm run build:services && npm run build:frontend", "build:gateway": "cd api-gateway && npm run build", "build:services": "npm run build:core && npm run build:extended", "build:core": "npm run build:doctor && npm run build:patient && npm run build:appointment", "build:extended": "npm run build:medical-records && npm run build:prescription && npm run build:billing && npm run build:notification", "build:doctor": "cd services/doctor-service && npm run build", "build:patient": "cd services/patient-service && npm run build", "build:appointment": "cd services/appointment-service && npm run build", "build:medical-records": "cd services/medical-records-service && npm run build", "build:prescription": "cd services/prescription-service && npm run build", "build:billing": "cd services/billing-service && npm run build", "build:room": "cd services/room-service && npm run build", "build:notification": "cd services/notification-service && npm run build", "build:file-storage": "cd services/file-storage-service && npm run build", "build:audit": "cd services/audit-service && npm run build", "build:chatbot": "cd services/chatbot-service && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:services && npm run test:gateway", "test:services": "npm run test:doctor && npm run test:patient && npm run test:appointment", "test:doctor": "cd services/doctor-service && npm test", "test:patient": "cd services/patient-service && npm test", "test:appointment": "cd services/appointment-service && npm test", "test:gateway": "cd api-gateway && npm test", "test:integration": "jest --config=jest.integration.config.js", "test:e2e": "jest --config=jest.e2e.config.js", "install:all": "npm install && npm run install:gateway && npm run install:services && npm run install:frontend", "install:gateway": "cd api-gateway && npm install", "install:services": "npm run install:doctor && npm run install:patient && npm run install:appointment", "install:doctor": "cd services/doctor-service && npm install", "install:patient": "cd services/patient-service && npm install", "install:appointment": "cd services/appointment-service && npm install", "install:frontend": "cd frontend && npm install", "migrate:all": "npm run migrate:doctor && npm run migrate:patient && npm run migrate:appointment", "migrate:doctor": "cd services/doctor-service && npm run migrate", "migrate:patient": "cd services/patient-service && npm run migrate", "migrate:appointment": "cd services/appointment-service && npm run migrate", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build", "docker:down": "docker-compose down", "docker:clean": "docker-compose down -v --rmi all", "lint": "npm run lint:gateway && npm run lint:services", "lint:gateway": "cd api-gateway && npm run lint", "lint:services": "npm run lint:doctor && npm run lint:patient && npm run lint:appointment", "lint:doctor": "cd services/doctor-service && npm run lint", "lint:patient": "cd services/patient-service && npm run lint", "lint:appointment": "cd services/appointment-service && npm run lint", "format": "prettier --write \"**/*.{js,ts,json,md}\"", "health-check": "node scripts/health-check.js", "cleanup:incomplete-users": "node scripts/cleanup-incomplete-users.js", "db:check": "node scripts/check-database-status.js", "db:setup": "node scripts/run-database-setup.js", "db:setup-foreign-keys": "echo 'Run add-foreign-keys.sql in Supabase SQL Editor'", "db:setup-rls": "echo 'Run setup-rls-policies.sql and setup-rls-policies-part2.sql in Supabase SQL Editor'", "db:setup-indexes": "echo 'Run add-performance-indexes.sql in Supabase SQL Editor'", "logs": "docker-compose logs -f", "logs:doctor": "docker-compose logs -f doctor-service", "logs:patient": "docker-compose logs -f patient-service", "logs:appointment": "docker-compose logs -f appointment-service"}, "workspaces": ["api-gateway", "services/*", "shared", "frontend"], "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "jest": "^29.6.0", "prettier": "^3.0.0", "typescript": "^5.1.0"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "axios": "^1.9.0", "dotenv": "^16.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/hospital-microservices.git"}, "keywords": ["hospital", "management", "microservices", "healthcare", "nodejs", "typescript", "docker", "kubernetes"], "author": "Your Name", "license": "MIT"}