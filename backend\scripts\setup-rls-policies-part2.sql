-- Hospital Management System - RLS Policies Part 2
-- Thiết lập policies cho các bảng còn lại

-- ============================================================================
-- PRESCRIPTIONS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Doctors can manage prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Admins can manage all prescriptions" ON prescriptions;
DROP POLICY IF EXISTS "Service role full access prescriptions" ON prescriptions;

-- Patients can view their own prescriptions
CREATE POLICY "Patients can view own prescriptions" ON prescriptions
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
    );

-- Doctors can manage prescriptions
CREATE POLICY "Doctors can manage prescriptions" ON prescriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

-- Admins can manage all prescriptions
CREATE POLICY "Admins can manage all prescriptions" ON prescriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access prescriptions" ON prescriptions
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- BILLING TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own billing" ON billing;
DROP POLICY IF EXISTS "Doctors can view billing" ON billing;
DROP POLICY IF EXISTS "Admins can manage all billing" ON billing;
DROP POLICY IF EXISTS "Service role full access billing" ON billing;

-- Patients can view their own billing
CREATE POLICY "Patients can view own billing" ON billing
    FOR SELECT USING (
        patient_id IN (SELECT patient_id FROM patients WHERE profile_id = auth.uid())
    );

-- Doctors can view billing (read-only)
CREATE POLICY "Doctors can view billing" ON billing
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'doctor'
        )
    );

-- Admins can manage all billing
CREATE POLICY "Admins can manage all billing" ON billing
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access billing" ON billing
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- PAYMENTS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own payments" ON payments;
DROP POLICY IF EXISTS "Admins can manage all payments" ON payments;
DROP POLICY IF EXISTS "Service role full access payments" ON payments;

-- Patients can view their own payments
CREATE POLICY "Patients can view own payments" ON payments
    FOR SELECT USING (
        bill_id IN (
            SELECT bill_id FROM billing 
            WHERE patient_id IN (
                SELECT patient_id FROM patients WHERE profile_id = auth.uid()
            )
        )
    );

-- Admins can manage all payments
CREATE POLICY "Admins can manage all payments" ON payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access payments" ON payments
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- SCHEDULES TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Doctors can manage own schedules" ON schedules;
DROP POLICY IF EXISTS "Patients can view doctor schedules" ON schedules;
DROP POLICY IF EXISTS "Admins can manage all schedules" ON schedules;
DROP POLICY IF EXISTS "Service role full access schedules" ON schedules;

-- Doctors can manage their own schedules
CREATE POLICY "Doctors can manage own schedules" ON schedules
    FOR ALL USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

-- Patients can view doctor schedules (for booking appointments)
CREATE POLICY "Patients can view doctor schedules" ON schedules
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

-- Admins can manage all schedules
CREATE POLICY "Admins can manage all schedules" ON schedules
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access schedules" ON schedules
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- AVAILABILITY TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Doctors can manage own availability" ON availability;
DROP POLICY IF EXISTS "Patients can view doctor availability" ON availability;
DROP POLICY IF EXISTS "Admins can manage all availability" ON availability;
DROP POLICY IF EXISTS "Service role full access availability" ON availability;

-- Doctors can manage their own availability
CREATE POLICY "Doctors can manage own availability" ON availability
    FOR ALL USING (
        doctor_id IN (SELECT doctor_id FROM doctors WHERE profile_id = auth.uid())
    );

-- Patients can view doctor availability (for booking appointments)
CREATE POLICY "Patients can view doctor availability" ON availability
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'patient'
        )
    );

-- Admins can manage all availability
CREATE POLICY "Admins can manage all availability" ON availability
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access availability" ON availability
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- DEPARTMENTS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "All authenticated users can view departments" ON departments;
DROP POLICY IF EXISTS "Admins can manage departments" ON departments;
DROP POLICY IF EXISTS "Service role full access departments" ON departments;

-- All authenticated users can view departments
CREATE POLICY "All authenticated users can view departments" ON departments
    FOR SELECT USING (auth.role() = 'authenticated');

-- Admins can manage departments
CREATE POLICY "Admins can manage departments" ON departments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access departments" ON departments
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- ROOMS TABLE POLICIES
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "All authenticated users can view rooms" ON rooms;
DROP POLICY IF EXISTS "Admins can manage rooms" ON rooms;
DROP POLICY IF EXISTS "Service role full access rooms" ON rooms;

-- All authenticated users can view rooms
CREATE POLICY "All authenticated users can view rooms" ON rooms
    FOR SELECT USING (auth.role() = 'authenticated');

-- Admins can manage rooms
CREATE POLICY "Admins can manage rooms" ON rooms
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Service role has full access
CREATE POLICY "Service role full access rooms" ON rooms
    FOR ALL USING (auth.role() = 'service_role');

-- ============================================================================
-- MEDICAL RECORD RELATED TABLES POLICIES
-- ============================================================================

-- Medical Record Attachments
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'medical_record_attachments') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view related medical record attachments" ON medical_record_attachments;
        DROP POLICY IF EXISTS "Doctors can manage medical record attachments" ON medical_record_attachments;
        DROP POLICY IF EXISTS "Admins can manage all medical record attachments" ON medical_record_attachments;
        DROP POLICY IF EXISTS "Service role full access medical record attachments" ON medical_record_attachments;

        -- Users can view attachments of their medical records
        CREATE POLICY "Users can view related medical record attachments" ON medical_record_attachments
            FOR SELECT USING (
                record_id IN (
                    SELECT record_id FROM medical_records 
                    WHERE patient_id IN (
                        SELECT patient_id FROM patients WHERE profile_id = auth.uid()
                    )
                )
                OR EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role IN ('doctor', 'admin')
                )
            );

        -- Doctors can manage medical record attachments
        CREATE POLICY "Doctors can manage medical record attachments" ON medical_record_attachments
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'doctor'
                )
            );

        -- Admins can manage all medical record attachments
        CREATE POLICY "Admins can manage all medical record attachments" ON medical_record_attachments
            FOR ALL USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );

        -- Service role has full access
        CREATE POLICY "Service role full access medical record attachments" ON medical_record_attachments
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

RAISE NOTICE 'RLS policies setup completed for all remaining tables!';
