-- Hospital Management System - Database Helper Functions
-- Các function hỗ trợ kiểm tra database status

-- Function để lấy thông tin foreign keys
CREATE OR REPLACE FUNCTION get_foreign_keys()
RETURNS TABLE (
    table_name text,
    column_name text,
    foreign_table_name text,
    foreign_column_name text,
    constraint_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tc.table_name::text,
        kcu.column_name::text,
        ccu.table_name::text AS foreign_table_name,
        ccu.column_name::text AS foreign_column_name,
        tc.constraint_name::text
    FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public'
    ORDER BY tc.table_name, kcu.column_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin RLS policies
CREATE OR REPLACE FUNCTION get_rls_policies()
RETURNS TABLE (
    schemaname text,
    tablename text,
    policyname text,
    permissive text,
    roles text[],
    cmd text,
    qual text,
    with_check text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.schemaname::text,
        p.tablename::text,
        p.policyname::text,
        p.permissive::text,
        p.roles,
        p.cmd::text,
        p.qual::text,
        p.with_check::text
    FROM pg_policies p
    WHERE p.schemaname = 'public'
    ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin indexes
CREATE OR REPLACE FUNCTION get_table_indexes()
RETURNS TABLE (
    schemaname text,
    tablename text,
    indexname text,
    indexdef text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.nspname::text AS schemaname,
        t.relname::text AS tablename,
        i.relname::text AS indexname,
        pg_get_indexdef(i.oid)::text AS indexdef
    FROM pg_index x
    JOIN pg_class i ON i.oid = x.indexrelid
    JOIN pg_class t ON t.oid = x.indrelid
    JOIN pg_namespace n ON n.oid = t.relnamespace
    WHERE n.nspname = 'public'
      AND NOT x.indisprimary  -- Exclude primary key indexes
      AND i.relname NOT LIKE '%_pkey'  -- Exclude primary key indexes
    ORDER BY t.relname, i.relname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để kiểm tra table có tồn tại không
CREATE OR REPLACE FUNCTION table_exists(table_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy số lượng rows trong table
CREATE OR REPLACE FUNCTION get_table_row_count(table_name text)
RETURNS bigint AS $$
DECLARE
    row_count bigint;
BEGIN
    EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO row_count;
    RETURN row_count;
EXCEPTION
    WHEN OTHERS THEN
        RETURN -1;  -- Return -1 if table doesn't exist or error
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để lấy thông tin columns của table
CREATE OR REPLACE FUNCTION get_table_columns(table_name text)
RETURNS TABLE (
    column_name text,
    data_type text,
    is_nullable text,
    column_default text,
    is_primary_key boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.column_name::text,
        c.data_type::text,
        c.is_nullable::text,
        c.column_default::text,
        CASE 
            WHEN pk.column_name IS NOT NULL THEN true 
            ELSE false 
        END as is_primary_key
    FROM information_schema.columns c
    LEFT JOIN (
        SELECT ku.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku
            ON tc.constraint_name = ku.constraint_name
            AND tc.table_schema = ku.table_schema
        WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = $1
    ) pk ON c.column_name = pk.column_name
    WHERE c.table_schema = 'public'
        AND c.table_name = $1
    ORDER BY c.ordinal_position;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function để kiểm tra RLS có được enable không
CREATE OR REPLACE FUNCTION check_rls_status()
RETURNS TABLE (
    table_name text,
    rls_enabled boolean,
    rls_forced boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.relname::text as table_name,
        c.relrowsecurity as rls_enabled,
        c.relforcerowsecurity as rls_forced
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public'
        AND c.relkind = 'r'  -- Only tables
    ORDER BY c.relname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_foreign_keys() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_rls_policies() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_table_indexes() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION table_exists(text) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_table_row_count(text) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_table_columns(text) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION check_rls_status() TO authenticated, anon;
