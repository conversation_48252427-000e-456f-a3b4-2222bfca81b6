# 🏥 Hospital Management System - Database Setup Summary

## 📋 Tổng quan

Tôi đã tạo một bộ scripts hoàn chỉnh để kiểm tra và cải thiện database Supabase của bạn với:

### ✅ Đã tạo
1. **Scripts kiểm tra database** - Kiểm tra trạng thái hiện tại
2. **Foreign Key Relationships** - <PERSON><PERSON><PERSON> liên hệ giữa các bảng
3. **Row Level Security (RLS) Policies** - Bảo mật cấp dòng
4. **Performance Indexes** - Tối ưu hiệu suất
5. **Helper Functions** - Các function hỗ trợ monitoring

## 🚀 Cách thực hiện

### Bước 1: Thiết lập Environment Variables

Tạo file `.env` trong thư mục `backend` hoặc export variables:

```bash
export NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

### Bước 2: Kiểm tra trạng thái hiện tại

```bash
cd backend
npm run db:check
```

Hoặc:

```bash
node scripts/check-database-status.js
```

### Bước 3: Chạy setup tự động (Khuyến nghị)

```bash
npm run db:setup
```

Hoặc:

```bash
node scripts/run-database-setup.js
```

### Bước 4: Kiểm tra lại sau khi setup

```bash
npm run db:check
```

## 📁 Files đã tạo

```
backend/scripts/
├── README.md                           # Hướng dẫn chi tiết
├── check-database-status.js            # ✅ Kiểm tra trạng thái database
├── run-database-setup.js               # ✅ Script chạy tự động
├── database-helper-functions.sql       # ✅ Helper functions
├── add-foreign-keys.sql                # ✅ Foreign key relationships
├── setup-rls-policies.sql              # ✅ RLS policies (Part 1)
├── setup-rls-policies-part2.sql        # ✅ RLS policies (Part 2)
├── add-performance-indexes.sql         # ✅ Performance indexes
└── setup-complete-database.sql         # ✅ Script tổng hợp
```

## 🔗 Foreign Key Relationships sẽ được thêm

### Core Relationships
- `doctors.profile_id` → `profiles.id`
- `doctors.department_id` → `departments.department_id`
- `patients.profile_id` → `profiles.id`
- `appointments.patient_id` → `patients.patient_id`
- `appointments.doctor_id` → `doctors.doctor_id`
- `appointments.room_id` → `rooms.room_id`
- `medical_records.patient_id` → `patients.patient_id`
- `medical_records.doctor_id` → `doctors.doctor_id`
- `medical_records.appointment_id` → `appointments.appointment_id`
- `prescriptions.patient_id` → `patients.patient_id`
- `prescriptions.doctor_id` → `doctors.doctor_id`
- `prescriptions.medical_record_id` → `medical_records.record_id`
- `billing.patient_id` → `patients.patient_id`
- `billing.appointment_id` → `appointments.appointment_id`
- `payments.bill_id` → `billing.bill_id`
- `schedules.doctor_id` → `doctors.doctor_id`
- `availability.doctor_id` → `doctors.doctor_id`
- `rooms.department_id` → `departments.department_id`

### Medical Records Relationships
- `medical_record_attachments.record_id` → `medical_records.record_id`
- `lab_results.record_id` → `medical_records.record_id`
- `vital_signs_history.record_id` → `medical_records.record_id`
- `medical_record_templates.created_by` → `profiles.id`

## 🔒 RLS Policies sẽ được thiết lập

### User Roles
- **Admin**: Full access to all data
- **Doctor**: Access to their patients' data and their own data  
- **Patient**: Access to their own data only

### Key Policies
- **Profiles**: Users can view/update own profile, admins can manage all
- **Doctors**: Doctors can manage own data, patients can view doctor info
- **Patients**: Patients can manage own data, doctors can view patient data
- **Appointments**: Users can view own appointments, role-based management
- **Medical Records**: Patients can view own, doctors can manage
- **Prescriptions**: Patients can view own, doctors can manage
- **Billing/Payments**: Patients can view own, admins can manage

## 📊 Performance Indexes sẽ được thêm

### Core Indexes
- **profiles**: role, email_verified, is_active
- **doctors**: profile_id, specialization, status, department_id
- **patients**: profile_id, date_of_birth, gender
- **appointments**: patient_id, doctor_id, appointment_date, status
- **medical_records**: patient_id, doctor_id, visit_date
- **prescriptions**: patient_id, doctor_id, prescription_date
- **billing**: patient_id, status, due_date
- **payments**: bill_id, payment_date, payment_method

### Composite Indexes
- `appointments(doctor_id, appointment_date)`
- `medical_records(patient_id, visit_date)`
- `billing(patient_id, status)`
- `prescriptions(patient_id, prescription_date)`

## 🛠️ Helper Functions sẽ được tạo

- `get_foreign_keys()` - Lấy danh sách foreign keys
- `get_rls_policies()` - Lấy danh sách RLS policies  
- `get_table_indexes()` - Lấy danh sách indexes
- `table_exists(table_name)` - Kiểm tra table có tồn tại
- `get_table_row_count(table_name)` - Đếm số rows trong table
- `get_table_columns(table_name)` - Lấy thông tin columns
- `check_rls_status()` - Kiểm tra RLS có được enable

## 📋 NPM Scripts đã thêm

```json
{
  "db:check": "node scripts/check-database-status.js",
  "db:setup": "node scripts/run-database-setup.js",
  "db:setup-foreign-keys": "echo 'Run add-foreign-keys.sql in Supabase SQL Editor'",
  "db:setup-rls": "echo 'Run setup-rls-policies.sql and setup-rls-policies-part2.sql in Supabase SQL Editor'",
  "db:setup-indexes": "echo 'Run add-performance-indexes.sql in Supabase SQL Editor'"
}
```

## ⚠️ Lưu ý quan trọng

### Trước khi chạy
1. **Backup database** trước khi thực hiện
2. **Kiểm tra environment variables** đã được thiết lập đúng
3. **Đảm bảo có quyền service_role** trong Supabase

### Sau khi chạy
1. **Test tất cả user roles** (admin, doctor, patient)
2. **Kiểm tra performance** của các queries
3. **Monitor RLS policies** hoạt động đúng
4. **Verify foreign key constraints** không gây lỗi

## 🔍 Kiểm tra kết quả

Sau khi chạy setup, bạn sẽ thấy:

```
📊 DATABASE STATUS SUMMARY
==========================================
📋 Total Tables: 12+
📊 Tables with Data: X
🔗 Foreign Keys: 20+
🔒 RLS Policies: 50+
📊 Indexes: 30+
```

## 🚨 Troubleshooting

### Nếu gặp lỗi Foreign Key
- Kiểm tra dữ liệu có tồn tại trong bảng reference
- Xóa dữ liệu không hợp lệ trước khi thêm constraint

### Nếu RLS Policies không hoạt động
- Kiểm tra auth context
- Verify user role trong profiles table
- Test với service_role key

### Nếu Performance chậm
- Review và optimize indexes
- Monitor query execution plans
- Adjust indexes based on usage patterns

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong console
2. Kiểm tra Supabase dashboard
3. Review RLS policies và permissions
4. Kiểm tra foreign key constraints

---

**Sẵn sàng để chạy!** 🚀

Chạy lệnh sau để bắt đầu:

```bash
cd backend
npm run db:check
npm run db:setup
```
